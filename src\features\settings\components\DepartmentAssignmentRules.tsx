'use client';

import { useState, useMemo, memo } from 'react';
import { Label } from '@/features/shared/components/ui/label';
import { Badge } from '@/features/shared/components/ui/badge';
import { Switch } from '@/features/shared/components/ui/switch';
import { UserAutocomplete } from '@/features/shared/components/UserAutocomplete';
import { AvatarGroup } from '@/features/shared/components/AvatarGroup';
import { useAdminSettings } from '../hooks/useSettingsSync';
import { useUserDetails } from '@/features/shared/hooks/useUserSearch';
import { useSettingsStore } from '../store/use-settings-store';
import { DEPARTMENT_CONFIG, type Department } from '../models/settings.schema';
import { cn } from '@/lib/utils';
import { AlertCircle, Users, ArrowRight } from 'lucide-react';
import { toast } from '@/features/shared/components/toast';

interface DepartmentRule {
  id?: string | undefined;
  department: Department;
  assigned_agent_id: string | null;
  is_active: boolean;
}

const DepartmentAssignmentRulesComponent = () => {
  const { adminSettings } = useAdminSettings();
  const { updateDepartmentRuleOptimistic } = useSettingsStore();
  const [updatingRule, setUpdatingRule] = useState<string | null>(null);

  // Derive rules directly from store instead of using local state
  const rules = useMemo((): DepartmentRule[] => {
    const currentRules = adminSettings?.auto_assignment_rules || [];
    return Object.keys(DEPARTMENT_CONFIG).map((dept) => {
      const department = dept as Department;
      const existingRule = currentRules.find(
        (rule) => rule.department === department
      );

      return {
        id: existingRule?.id,
        department,
        assigned_agent_id: existingRule?.assigned_agent_id || null,
        is_active: existingRule?.is_active ?? false,
      };
    });
  }, [adminSettings?.auto_assignment_rules]);

  // Get agent details for display
  const assignedAgentIds = useMemo(() => {
    return rules
      .filter((rule) => rule.assigned_agent_id)
      .map((rule) => rule.assigned_agent_id!);
  }, [rules]);

  const { users: agentDetails } = useUserDetails(assignedAgentIds);

  // Update department rule on server with proper Supabase synchronization
  const updateDepartmentRule = async (
    department: Department,
    assigned_agent_id: string | null,
    is_active: boolean
  ) => {
    setUpdatingRule(department);

    try {
      // Use optimistic update from store
      const result = await updateDepartmentRuleOptimistic(
        department,
        assigned_agent_id,
        is_active
      );

      // Show success toast after confirmed update
      toast.success(result.message || 'Department rule updated successfully');
    } catch (error) {
      console.error('Department rule update error:', error);
      toast.error('Update Failed', {
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update department rule',
      });
    } finally {
      setUpdatingRule(null);
    }
  };

  const handleAgentChange = async (
    department: Department,
    agentId: string | string[]
  ) => {
    const newAgentId = Array.isArray(agentId)
      ? agentId[0] || null
      : agentId || null;

    // When agent is assigned, automatically set to active
    // When agent is removed, set to inactive
    const isActive = !!newAgentId;

    await updateDepartmentRule(department, newAgentId, isActive);
  };

  const handleToggleActive = async (department: Department) => {
    const rule = rules.find((r) => r.department === department);
    if (!rule || !rule.assigned_agent_id) return;

    await updateDepartmentRule(
      department,
      rule.assigned_agent_id,
      !rule.is_active
    );
  };

  // Group agents by their assignments for avatar display
  const agentAssignments = useMemo(() => {
    const assignments = new Map<
      string,
      { agent: any; departments: Department[] }
    >();

    rules
      .filter((rule) => rule.is_active && rule.assigned_agent_id)
      .forEach((rule) => {
        const agentId = rule.assigned_agent_id!;
        const agent = agentDetails.find((a) => a.id === agentId);

        if (agent) {
          if (assignments.has(agentId)) {
            assignments.get(agentId)!.departments.push(rule.department);
          } else {
            assignments.set(agentId, { agent, departments: [rule.department] });
          }
        }
      });

    return Array.from(assignments.values());
  }, [rules, agentDetails]);

  return (
    <div className='space-y-6'>
      {/* Status Overview */}
      <div className='flex items-center gap-3 p-4 bg-muted/50 rounded-lg'>
        {/* Avatar Display - Show multiple overlapping avatars */}
        {agentAssignments.length > 0 ? (
          <AvatarGroup
            agentAssignments={agentAssignments}
            maxVisible={4}
            size='md'
            formatDepartments={(departments) =>
              departments
                .map((dept) => DEPARTMENT_CONFIG[dept as Department].label)
                .join(', ')
            }
          />
        ) : (
          <Users className='w-10 h-10 text-muted-foreground p-2 bg-muted rounded-full' />
        )}

        <div className='flex-1'>
          <h4 className='text-sm font-medium'>Department Assignment Rules</h4>
          <p className='text-xs text-muted-foreground mt-1'>
            {agentAssignments.length > 0
              ? `${agentAssignments.length} agent${agentAssignments.length === 1 ? '' : 's'} assigned to departments`
              : 'No department assignment rules are currently active'}
          </p>
        </div>

        <Badge variant={agentAssignments.length > 0 ? 'default' : 'secondary'}>
          {agentAssignments.length} Active
        </Badge>
      </div>

      {/* Department Rules */}
      <div className='space-y-4'>
        <Label className='text-sm font-medium'>
          Configure Department Rules
        </Label>

        <div className='grid gap-4'>
          {rules.map((rule) => {
            const config = DEPARTMENT_CONFIG[rule.department];

            return (
              <div
                key={rule.department}
                className={cn(
                  'p-4 transition-all duration-200 bg-white dark:bg-gray-800',
                  'rounded-lg border shadow-sm',
                  rule.is_active
                    ? 'border-primary/20'
                    : 'border-gray-200 dark:border-gray-700'
                )}
              >
                <div className='space-y-4'>
                  {/* Department Header */}
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>
                      <Badge className={cn('text-xs', config.color)}>
                        <div
                          className={cn(
                            'w-1.5 h-1.5 rounded-full mr-1.5',
                            config.dotColor
                          )}
                        />
                        {config.label}
                      </Badge>
                      <ArrowRight className='w-4 h-4 text-muted-foreground' />

                      {/* Agent Display - Show name only, no avatar */}
                      {rule.assigned_agent_id ? (
                        <div className='flex items-center gap-2'>
                          {(() => {
                            const agent = agentDetails.find(
                              (a) => a.id === rule.assigned_agent_id
                            );
                            return (
                              <span
                                className={cn(
                                  'text-sm font-medium',
                                  !rule.is_active && 'text-muted-foreground'
                                )}
                              >
                                {agent?.name || 'Unknown Agent'}
                              </span>
                            );
                          })()}
                        </div>
                      ) : (
                        <span className='text-sm text-muted-foreground'>
                          No Agent Assigned
                        </span>
                      )}
                    </div>

                    {/* Controls */}
                    <div className='flex items-center gap-2'>
                      {rule.assigned_agent_id ? (
                        <>
                          {/* Active/Inactive Toggle */}
                          <Switch
                            checked={rule.is_active}
                            onCheckedChange={() =>
                              handleToggleActive(rule.department)
                            }
                            disabled={updatingRule === rule.department}
                          />
                          <span
                            className={cn(
                              'text-xs font-medium min-w-[50px]',
                              rule.is_active
                                ? 'text-green-600 dark:text-green-400'
                                : 'text-gray-500 dark:text-gray-400'
                            )}
                          >
                            {rule.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </>
                      ) : (
                        <div className='flex items-center gap-2'>
                          <div className='w-2 h-2 rounded-full bg-yellow-500' />
                          <span className='text-xs text-yellow-600 dark:text-yellow-400 font-medium'>
                            Not Set
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Agent Selection */}
                  <div className='space-y-2'>
                    <Label className='text-xs text-muted-foreground'>
                      Select agent for {config.label} tickets
                    </Label>

                    {rule.assigned_agent_id ? (
                      // Show selected agent with option to change - matches Administration styling
                      <div className='file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring items-center justify-between'>
                        <span className='text-gray-900 dark:text-gray-100'>
                          {(() => {
                            const agent = agentDetails.find(
                              (a) => a.id === rule.assigned_agent_id
                            );
                            // Show only email like in Administration
                            if (agent?.email && agent.email.includes('@')) {
                              return agent.email;
                            }
                            return agent
                              ? 'Email not available'
                              : 'Loading agent details...';
                          })()}
                        </span>
                        <button
                          onClick={() => handleAgentChange(rule.department, '')}
                          disabled={updatingRule === rule.department}
                          className='h-4 w-4 cursor-pointer hover:bg-gray-300 rounded-full text-gray-500 hover:text-gray-700 disabled:opacity-50 flex items-center justify-center text-xs'
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      // Show agent selector when no agent is assigned
                      <UserAutocomplete
                        value=''
                        onChange={(value) =>
                          handleAgentChange(rule.department, value)
                        }
                        placeholder={`Select agent for ${config.label} department...`}
                        roleFilter={['admin', 'agent']}
                        multiple={false}
                        dropdownOnly={true}
                        returnUserIds={true}
                        disabled={updatingRule === rule.department}
                        className='h-10'
                      />
                    )}
                  </div>

                  {/* Rule Description */}
                  <p className='text-xs text-muted-foreground'>
                    {rule.is_active && rule.assigned_agent_id
                      ? `All new ${config.label.toLowerCase()} tickets will be automatically assigned to ${(() => {
                          const agent = agentDetails.find(
                            (a) => a.id === rule.assigned_agent_id
                          );
                          return agent?.name || 'the selected agent';
                        })()}.`
                      : !rule.assigned_agent_id
                        ? `Select an agent to enable auto-assignment for ${config.label.toLowerCase()} tickets. Without an agent, tickets will use the default agent from Administration.`
                        : `Agent will remain assigned but inactive for new ${config.label.toLowerCase()} tickets. Tickets will use the default agent from Administration.`}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Information Box */}
      <div className='bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4'>
        <div className='flex items-start gap-3'>
          <AlertCircle className='w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0' />
          <div className='space-y-2'>
            <h4 className='text-sm font-medium text-blue-900 dark:text-blue-100'>
              Assignment Priority Rules
            </h4>
            <ul className='text-xs text-blue-800 dark:text-blue-200 space-y-1'>
              <li>
                • Department-specific rules take priority over default agent
                assignment
              </li>
              <li>• Only active rules with assigned agents will be applied</li>
              <li>
                • Inactive agents remain visible but won't receive new tickets
              </li>
              <li>
                • Use the agent selector to change or remove agents from
                departments
              </li>
              <li>• Changes are saved instantly and take effect immediately</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export const DepartmentAssignmentRules = memo(
  DepartmentAssignmentRulesComponent
);
