import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Department rule update schema
const DepartmentRuleUpdateSchema = z.object({
  department: z.enum(['sales', 'support', 'marketing', 'technical']),
  assigned_agent_id: z.string().uuid().nullable(),
  is_active: z.boolean(),
});

// PUT - Update a single department rule with instant server update
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = DepartmentRuleUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid department rule data',
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { department, assigned_agent_id, is_active } = validation.data;

    const serviceClient = createServiceSupabaseClient();

    // Get current user info
    const { data: currentUser, error: userError } = await serviceClient
      .from('users')
      .select('id, tenant_id, role')
      .eq('clerk_id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions
    if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // If agent is being removed (assigned_agent_id is null), delete the rule
    if (!assigned_agent_id) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (serviceClient as any)
        .from('auto_assignment_rules')
        .delete()
        .eq('tenant_id', currentUser.tenant_id)
        .eq('department', department);

      return NextResponse.json({
        success: true,
        message: `Agent removed from ${department} department`,
        data: null,
      });
    }

    // Verify the assigned agent exists and has proper role
    const { data: agent, error: agentError } = await serviceClient
      .from('users')
      .select('id, email, role')
      .eq('id', assigned_agent_id)
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (agentError || !agent) {
      return NextResponse.json(
        { error: 'Assigned agent not found' },
        { status: 404 }
      );
    }

    if (agent.role !== 'admin' && agent.role !== 'agent') {
      return NextResponse.json(
        { error: 'Assigned user must have admin or agent role' },
        { status: 400 }
      );
    }

    // Check if rule already exists
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: existingRule } = await (serviceClient as any)
      .from('auto_assignment_rules')
      .select('id')
      .eq('tenant_id', currentUser.tenant_id)
      .eq('department', department)
      .single();

    let updatedRule;
    let updateError;

    if (existingRule) {
      // Update existing rule
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (serviceClient as any)
        .from('auto_assignment_rules')
        .update({
          assigned_agent_id,
          is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('tenant_id', currentUser.tenant_id)
        .eq('department', department)
        .select()
        .single();

      updatedRule = result.data;
      updateError = result.error;
    } else {
      // Insert new rule
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (serviceClient as any)
        .from('auto_assignment_rules')
        .insert({
          tenant_id: currentUser.tenant_id,
          department,
          assigned_agent_id,
          is_active,
          priority: 1, // Default priority
          created_by: currentUser.id,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      updatedRule = result.data;
      updateError = result.error;
    }

    if (updateError) {
      console.error('Department rule update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update department rule' },
        { status: 500 }
      );
    }

    // Determine the appropriate message based on the action
    let message: string;
    if (is_active) {
      message = `Agent activated for ${department} department`;
    } else {
      message = `Agent deactivated for ${department} department`;
    }

    return NextResponse.json({
      success: true,
      message,
      data: {
        rule: updatedRule,
        agent: {
          id: agent.id,
          email: agent.email,
        },
      },
    });
  } catch (error) {
    console.error('Department rule update error:', error);
    return NextResponse.json(
      { error: 'Failed to update department rule' },
      { status: 500 }
    );
  }
}
