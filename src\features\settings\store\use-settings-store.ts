'use client';

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { useShallow } from 'zustand/react/shallow';
import {
  AdminSettings,
  AdminSettingsUpdate,
  SettingsResponse,
  UserSettings,
  UserSettingsUpdate,
} from '../models/settings.schema';
import { SettingsCacheService } from '../services/settings-cache.service';
import {
  SettingsErrorHandler,
  SettingsPerformanceMonitor,
} from '../utils/error-handler';

interface SettingsState {
  // Core state
  userSettings: UserSettings | null;
  adminSettings: AdminSettings | null;
  isLoading: boolean;
  error: string | null;

  // Cache state
  isCacheLoaded: boolean;
  lastSync: number;

  // UI state
  isSettingsOpen: boolean;
  activeSection: string;

  // Optimistic updates
  optimisticUpdates: Record<string, unknown>;
}

interface SettingsActions {
  // Settings management
  loadSettings: (tenantId: string, userId?: string) => Promise<void>;
  updateUserSettings: (
    updates: UserSettingsUpdate,
    tenantId?: string,
    userId?: string
  ) => Promise<void>;
  updateAdminSettings: (updates: AdminSettingsUpdate) => Promise<void>;

  // UI actions
  openSettings: (section?: string) => void;
  closeSettings: () => void;
  setActiveSection: (section: string) => void;

  // Cache management
  loadFromCache: (tenantId: string) => Promise<void>;
  syncToCache: () => Promise<void>;
  clearCache: () => void;

  // Department rules optimistic updates
  updateDepartmentRuleOptimistic: (
    department: 'sales' | 'support' | 'marketing' | 'technical',
    assigned_agent_id: string | null,
    is_active: boolean
  ) => Promise<any>;

  // Utility actions
  reset: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

const initialState: SettingsState = {
  userSettings: null,
  adminSettings: null,
  isLoading: false,
  error: null,
  isCacheLoaded: false,
  lastSync: 0,
  isSettingsOpen: false,
  activeSection: 'profile',
  optimisticUpdates: {},
};

export const useSettingsStore = create<SettingsState & SettingsActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Load settings from API
        loadSettings: async (tenantId: string, userId?: string) => {
          if (!tenantId) return;

          const errorHandler = SettingsErrorHandler.getInstance();
          const performanceMonitor = SettingsPerformanceMonitor.getInstance();
          const cacheService = SettingsCacheService.getInstance();

          // Check cache first to determine if we need loading state
          let hasCachedData = false;
          if (userId) {
            const cachedSettings = await cacheService.getFromCache(
              tenantId,
              userId
            );
            if (cachedSettings) {
              hasCachedData = true;
              set({
                userSettings: cachedSettings.user_settings,
                adminSettings: cachedSettings.admin_settings || null,
                isCacheLoaded: true,
                error: null,
                isLoading: false, // Don't show loading if we have cached data
              });
            }
          }

          // Only set loading state if we don't have cached data
          if (!hasCachedData) {
            set({ isLoading: true, error: null });
          }

          await performanceMonitor.measureOperation(async () => {
            try {
              // Then fetch fresh data with retry logic
              const response = await errorHandler.retryOperation(
                () => fetch('/api/settings'),
                2, // Max 2 retries
                500 // 500ms base delay
              );

              if (!response.ok) {
                throw new Error(
                  `HTTP ${response.status}: Failed to fetch settings`
                );
              }

              const data: { success: boolean; data: SettingsResponse } =
                await response.json();

              if (data.success) {
                set({
                  userSettings: data.data.user_settings,
                  adminSettings: data.data.admin_settings || null,
                  lastSync: Date.now(),
                  error: null,
                  isCacheLoaded: false,
                });

                // Update cache
                if (userId) {
                  try {
                    await cacheService.storeInCache(
                      tenantId,
                      userId,
                      data.data
                    );
                  } catch (cacheError) {
                    errorHandler.handleCacheError(cacheError, 'store');
                  }
                }
              } else {
                throw new Error('API returned unsuccessful response');
              }
            } catch (error) {
              const settingsError = errorHandler.handleApiError(
                error,
                'loadSettings'
              );
              set({ error: settingsError.message });
            } finally {
              set({ isLoading: false });
            }
          }, 'loadSettings');
        },

        // Update user settings
        updateUserSettings: async (
          updates: UserSettingsUpdate,
          tenantId?: string,
          userId?: string
        ) => {
          const currentSettings = get().userSettings;
          if (!currentSettings) return;

          const errorHandler = SettingsErrorHandler.getInstance();
          const performanceMonitor = SettingsPerformanceMonitor.getInstance();
          const cacheService = SettingsCacheService.getInstance();

          // Simple optimistic update for theme changes
          const optimisticSettings: UserSettings = {
            ...currentSettings,
          };

          if (updates.theme_preference) {
            optimisticSettings.theme_preference = updates.theme_preference as
              | 'light'
              | 'dark'
              | 'system';
          }

          // Only update preferences if they are being changed
          if (updates.preferences) {
            // Ensure currentSettings has a valid preferences structure
            const currentPreferences = currentSettings.preferences || {
              notifications: { email: true, browser: true, sound: false },
              language: 'en',
              timezone: 'UTC',
              dateFormat: 'MM/DD/YYYY',
              timeFormat: '12h',
            };

            // Merge preferences safely
            optimisticSettings.preferences = {
              language:
                updates.preferences.language ?? currentPreferences.language,
              timezone:
                updates.preferences.timezone ?? currentPreferences.timezone,
              dateFormat:
                updates.preferences.dateFormat ?? currentPreferences.dateFormat,
              timeFormat:
                updates.preferences.timeFormat ?? currentPreferences.timeFormat,
              notifications: {
                email:
                  updates.preferences.notifications?.email ??
                  currentPreferences.notifications.email,
                browser:
                  updates.preferences.notifications?.browser ??
                  currentPreferences.notifications.browser,
                sound:
                  updates.preferences.notifications?.sound ??
                  currentPreferences.notifications.sound,
              },
            };
          }

          set({
            userSettings: optimisticSettings,
            optimisticUpdates: {
              ...get().optimisticUpdates,
              userSettings: updates,
            },
          });

          await performanceMonitor.measureOperation(async () => {
            try {
              const response = await errorHandler.retryOperation(
                () =>
                  fetch('/api/settings', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_settings: updates }),
                  }),
                2,
                500
              );

              if (!response.ok) {
                throw new Error(
                  `HTTP ${response.status}: Failed to update user settings`
                );
              }

              const data = await response.json();

              if (data.success) {
                set({
                  userSettings: data.data.user_settings,
                  optimisticUpdates: {
                    ...get().optimisticUpdates,
                    userSettings: undefined,
                  },
                  lastSync: Date.now(),
                });

                // Update cache
                if (tenantId && userId) {
                  await cacheService.updateUserSettingsInCache(
                    tenantId,
                    userId,
                    data.data.user_settings
                  );
                }
              }
            } catch (error) {
              // Revert optimistic update
              set({
                userSettings: currentSettings,
                optimisticUpdates: {
                  ...get().optimisticUpdates,
                  userSettings: undefined,
                },
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update settings',
              });
            }
          }, 'updateUserSettings');
        },

        // Update admin settings with optimistic updates
        updateAdminSettings: async (updates: AdminSettingsUpdate) => {
          const currentAdminSettings = get().adminSettings;

          // Create optimistic admin settings
          let optimisticAdminSettings: AdminSettings | null = null;

          if (currentAdminSettings) {
            optimisticAdminSettings = { ...currentAdminSettings };

            // Apply optimistic updates for default agent settings
            if (
              updates.default_agent_id !== undefined ||
              updates.default_agent_is_active !== undefined
            ) {
              // Skip optimistic updates for status-only changes to prevent flickering
              // Only apply optimistic updates when setting/removing agent ID
              if (updates.default_agent_id !== undefined) {
                if (!optimisticAdminSettings.default_agent_settings) {
                  // Create new settings when setting an agent ID
                  optimisticAdminSettings.default_agent_settings = {
                    id: '',
                    tenant_id: '',
                    default_agent_id: updates.default_agent_id,
                    is_active: updates.default_agent_is_active ?? true,
                    created_by: '',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                  };
                } else {
                  // Update existing settings when changing agent ID
                  const existingSettings = {
                    ...optimisticAdminSettings.default_agent_settings,
                  };
                  existingSettings.default_agent_id = updates.default_agent_id;
                  if (updates.default_agent_is_active !== undefined) {
                    existingSettings.is_active =
                      updates.default_agent_is_active;
                  }
                  existingSettings.updated_at = new Date().toISOString();
                  optimisticAdminSettings.default_agent_settings =
                    existingSettings;
                }
              }
              // Skip optimistic updates for status-only changes (default_agent_is_active only)
              // This prevents the flickering issue when toggling agent status
            }

            // Apply optimistic updates for auto assignment rules
            // Note: Optimistic updates for auto_assignment_rules are handled
            // by the dedicated department-rules API endpoint
            // if (updates.auto_assignment_rules) {
            //   optimisticAdminSettings.auto_assignment_rules =
            //     updates.auto_assignment_rules;
            // }
          }

          // Apply optimistic update immediately
          if (optimisticAdminSettings) {
            set({
              adminSettings: optimisticAdminSettings,
              optimisticUpdates: {
                ...get().optimisticUpdates,
                adminSettings: updates,
              },
            });
          }

          try {
            const response = await fetch('/api/settings/admin', {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(updates),
            });

            if (!response.ok) {
              throw new Error('Failed to update admin settings');
            }

            const data = await response.json();

            if (data.success) {
              // Update with server response and clear optimistic updates
              set({
                adminSettings: data.data,
                lastSync: Date.now(),
                optimisticUpdates: {
                  ...get().optimisticUpdates,
                  adminSettings: undefined,
                },
                error: null,
              });

              await get().syncToCache();
            }
          } catch (error) {
            // Rollback optimistic updates on error
            set({
              adminSettings: currentAdminSettings,
              optimisticUpdates: {
                ...get().optimisticUpdates,
                adminSettings: undefined,
              },
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to update admin settings',
            });
          }
        },

        // Update department rule with optimistic updates
        updateDepartmentRuleOptimistic: async (
          department: 'sales' | 'support' | 'marketing' | 'technical',
          assigned_agent_id: string | null,
          is_active: boolean
        ) => {
          const currentAdminSettings = get().adminSettings;

          if (!currentAdminSettings) {
            throw new Error('Admin settings not available');
          }

          // Create optimistic admin settings with updated rule
          const optimisticAdminSettings = { ...currentAdminSettings };
          const currentRules = [
            ...(currentAdminSettings.auto_assignment_rules || []),
          ];

          // Find existing rule or create new one
          const existingRuleIndex = currentRules.findIndex(
            (rule) => rule.department === department
          );

          if (existingRuleIndex >= 0) {
            // Update existing rule
            const existingRule = currentRules[existingRuleIndex];
            currentRules[existingRuleIndex] = {
              ...existingRule,
              assigned_agent_id,
              is_active,
              updated_at: new Date().toISOString(),
            } as any; // Type assertion for optimistic update
          } else {
            // Create new rule for optimistic update
            currentRules.push({
              id: `temp-${Date.now()}`,
              tenant_id: 'temp',
              department,
              assigned_agent_id,
              is_active,
              is_default: false,
              priority: 1,
              created_by: 'temp',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            } as any); // Type assertion for optimistic update
          }

          optimisticAdminSettings.auto_assignment_rules = currentRules;

          // Apply optimistic update immediately
          set({
            adminSettings: optimisticAdminSettings,
            optimisticUpdates: {
              ...get().optimisticUpdates,
              [`departmentRule_${department}`]: {
                assigned_agent_id,
                is_active,
              },
            },
          });

          try {
            const response = await fetch('/api/settings/department-rules', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                department,
                assigned_agent_id,
                is_active,
              }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(
                result.error || 'Failed to update department rule'
              );
            }

            // Clear optimistic update - real-time subscription will handle the final state
            // This prevents conflicts between optimistic updates and real-time updates
            // No need to reload settings as real-time updates provide instant synchronization
            set({
              optimisticUpdates: {
                ...get().optimisticUpdates,
                [`departmentRule_${department}`]: undefined,
              },
              error: null,
            });

            return result;
          } catch (error) {
            // Rollback optimistic update on error
            set({
              adminSettings: currentAdminSettings,
              optimisticUpdates: {
                ...get().optimisticUpdates,
                [`departmentRule_${department}`]: undefined,
              },
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to update department rule',
            });
            throw error;
          }
        },

        // UI actions
        openSettings: (section = 'profile') => {
          set({ isSettingsOpen: true, activeSection: section });
        },

        closeSettings: () => {
          set({ isSettingsOpen: false });
        },

        setActiveSection: (section: string) => {
          set({ activeSection: section });
        },

        // Cache management
        loadFromCache: async () => {
          try {
            // Implementation would use existing cache service
            // For now, just mark as cache loaded
            set({ isCacheLoaded: true });
          } catch (error) {
            console.warn('Failed to load settings from cache:', error);
          }
        },

        syncToCache: async () => {
          try {
            // Implementation would use existing cache service
            // For now, just update sync time
            set({ lastSync: Date.now() });
          } catch (error) {
            console.warn('Failed to sync settings to cache:', error);
          }
        },

        clearCache: () => {
          set({ isCacheLoaded: false, lastSync: 0 });
        },

        // Utility actions
        reset: () => {
          set(initialState);
        },

        setError: (error: string | null) => {
          set({ error });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },
      }),
      {
        name: 'settings-store',
        partialize: (state) => ({
          userSettings: state.userSettings,
          adminSettings: state.adminSettings,
          lastSync: state.lastSync,
          activeSection: state.activeSection,
        }),
      }
    ),
    { name: 'SettingsStore' }
  )
);

// Selector hooks for optimized re-renders
export const useUserSettings = () =>
  useSettingsStore(useShallow((state) => state.userSettings));

export const useAdminSettings = () =>
  useSettingsStore(useShallow((state) => state.adminSettings));

export const useSettingsLoading = () =>
  useSettingsStore((state) => state.isLoading);

export const useSettingsError = () => useSettingsStore((state) => state.error);

export const useSettingsOpen = () =>
  useSettingsStore((state) => state.isSettingsOpen);

export const useActiveSection = () =>
  useSettingsStore((state) => state.activeSection);

// Combined selectors
export const useSettingsData = () =>
  useSettingsStore(
    useShallow((state) => ({
      userSettings: state.userSettings,
      adminSettings: state.adminSettings,
      isLoading: state.isLoading,
      error: state.error,
    }))
  );

export const useSettingsUIState = () =>
  useSettingsStore(
    useShallow((state) => ({
      isSettingsOpen: state.isSettingsOpen,
      activeSection: state.activeSection,
    }))
  );

// Action hooks
export const useSettingsActions = () =>
  useSettingsStore(
    useShallow((state) => ({
      loadSettings: state.loadSettings,
      updateUserSettings: state.updateUserSettings,
      updateAdminSettings: state.updateAdminSettings,

      openSettings: state.openSettings,
      closeSettings: state.closeSettings,
      setActiveSection: state.setActiveSection,
      reset: state.reset,
      setError: state.setError,
    }))
  );

// Convenience hook for common operations
export const useSettings = () => {
  const settingsData = useSettingsData();
  const actions = useSettingsActions();

  return {
    ...settingsData,
    ...actions,
  };
};
