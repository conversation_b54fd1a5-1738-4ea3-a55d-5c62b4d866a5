'use client';

import { useSupabase } from '@/features/shared/components/SupabaseProvider';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useCallback, useEffect, useRef } from 'react';
import {
  AutoAssignmentRule,
  DefaultAgentSettings,
  UserSettings,
} from '../models/settings.schema';
import { useSettingsStore } from '../store/use-settings-store';
import { SettingsCacheService } from './settings-cache.service';

// Database row types for settings tables
type UserSettingsRow = UserSettings;
type DefaultAgentSettingsRow = DefaultAgentSettings;
type AutoAssignmentRuleRow = AutoAssignmentRule;

// Union type for all settings table rows
type SettingsTableRow =
  | UserSettingsRow
  | DefaultAgentSettingsRow
  | AutoAssignmentRuleRow;

interface SettingsRealtimeEvent {
  table: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: SettingsTableRow;
  old?: SettingsTableRow;
}

/**
 * Real-time settings synchronization service
 * Listens for changes to settings tables and updates local state/cache
 */
export function useSettingsRealtime(tenantId: string, userId: string) {
  const { supabase } = useSupabase();
  const { loadSettings } = useSettingsStore();
  const cacheService = SettingsCacheService.getInstance();
  const channelRef = useRef<{ unsubscribe: () => void } | null>(null);
  const lastSyncRef = useRef<number>(0);

  // Instant sync for real-time performance (removed debouncing for bleeding-fast updates)
  const instantSync = useCallback(async () => {
    lastSyncRef.current = Date.now();
    await loadSettings(tenantId, userId);
  }, [tenantId, userId, loadSettings]);

  // Handle user settings changes
  const handleUserSettingsChange = useCallback(
    async (payload: RealtimePostgresChangesPayload<UserSettingsRow>) => {
      if (
        !payload.new ||
        !('tenant_id' in payload.new) ||
        payload.new.tenant_id !== tenantId
      )
        return;

      try {
        // Update local state optimistically
        if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
          const updatedSettings = payload.new as UserSettings;

          // Only update if this is for the current user
          if (updatedSettings.user_id === userId) {
            useSettingsStore.setState({
              userSettings: updatedSettings,
              lastSync: Date.now(),
            });

            // Update cache
            await cacheService.updateUserSettingsInCache(
              tenantId,
              userId,
              updatedSettings
            );
          }
        } else if (payload.eventType === 'DELETE') {
          // Handle deletion (rare case)
          instantSync();
        }
      } catch (error) {
        console.warn('Failed to handle user settings realtime update:', error);
        // Fallback to instant sync
        instantSync();
      }
    },
    [tenantId, userId, cacheService, instantSync]
  );

  // Handle admin settings changes with granular updates for bleeding-fast performance
  const handleAdminSettingsChange = useCallback(
    async (
      payload: RealtimePostgresChangesPayload<
        DefaultAgentSettingsRow | AutoAssignmentRuleRow
      >
    ) => {
      if (
        !payload.new ||
        !('tenant_id' in payload.new) ||
        payload.new.tenant_id !== tenantId
      )
        return;

      try {
        const { table } = payload;

        if (table === 'default_agent_settings') {
          // Granular update for default agent settings
          if (
            payload.eventType === 'UPDATE' ||
            payload.eventType === 'INSERT'
          ) {
            const updatedSettings = payload.new as DefaultAgentSettings;
            const currentState = useSettingsStore.getState();

            // Update only the default agent settings part
            useSettingsStore.setState({
              adminSettings: {
                ...currentState.adminSettings,
                default_agent_id: updatedSettings.default_agent_id,
                default_agent_is_active: updatedSettings.is_active,
              },
              lastSync: Date.now(),
            });

            // Update cache with granular data
            await cacheService.updateAdminSettingsInCache(tenantId, {
              default_agent_id: updatedSettings.default_agent_id,
              default_agent_is_active: updatedSettings.is_active,
            });
          }
        } else if (table === 'auto_assignment_rules') {
          // Granular update for auto assignment rules
          if (
            payload.eventType === 'UPDATE' ||
            payload.eventType === 'INSERT'
          ) {
            const updatedRule = payload.new as AutoAssignmentRule;
            const currentState = useSettingsStore.getState();
            const currentRules =
              currentState.adminSettings?.auto_assignment_rules || [];

            // Update or add the specific rule
            const updatedRules = currentRules.filter(
              (rule) => rule.id !== updatedRule.id
            );
            updatedRules.push(updatedRule);

            useSettingsStore.setState({
              adminSettings: {
                ...currentState.adminSettings,
                auto_assignment_rules: updatedRules,
              },
              lastSync: Date.now(),
            });

            // Update cache with granular data
            await cacheService.updateAdminSettingsInCache(tenantId, {
              auto_assignment_rules: updatedRules,
            });
          } else if (payload.eventType === 'DELETE' && payload.old) {
            // Handle rule deletion
            const deletedRule = payload.old as AutoAssignmentRule;
            const currentState = useSettingsStore.getState();
            const currentRules =
              currentState.adminSettings?.auto_assignment_rules || [];

            const updatedRules = currentRules.filter(
              (rule) => rule.id !== deletedRule.id
            );

            useSettingsStore.setState({
              adminSettings: {
                ...currentState.adminSettings,
                auto_assignment_rules: updatedRules,
              },
              lastSync: Date.now(),
            });
          }
        }
      } catch (error) {
        console.warn('Failed to handle admin settings realtime update:', error);
        // Fallback to instant sync only if granular update fails
        instantSync();
      }
    },
    [tenantId, cacheService, instantSync]
  );

  // Set up real-time subscriptions
  useEffect(() => {
    if (!supabase || !tenantId || !userId) return;

    const channelName = `settings_${tenantId}_${userId}`;

    // Clean up existing channel
    if (channelRef.current) {
      channelRef.current.unsubscribe();
    }

    // Create new channel
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_settings',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleUserSettingsChange
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'default_agent_settings',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleAdminSettingsChange
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'auto_assignment_rules',
          filter: `tenant_id=eq.${tenantId}`,
        },
        handleAdminSettingsChange
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Settings realtime subscription active');
        } else if (status === 'CHANNEL_ERROR') {
          console.warn('Settings realtime subscription error');
        }
      });

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
        channelRef.current = null;
      }
    };
  }, [
    supabase,
    tenantId,
    userId,
    handleUserSettingsChange,
    handleAdminSettingsChange,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
    };
  }, []);

  return {
    isConnected: !!channelRef.current,
    lastSync: lastSyncRef.current,
  };
}

/**
 * Settings broadcast service for notifying other tabs/windows
 */
export class SettingsBroadcastService {
  private static instance: SettingsBroadcastService;
  private channel: BroadcastChannel | null = null;
  private listeners: Set<(event: SettingsRealtimeEvent) => void> = new Set();

  private constructor() {
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      this.channel = new BroadcastChannel('settings_sync');
      this.channel.addEventListener('message', this.handleMessage.bind(this));
    }
  }

  static getInstance(): SettingsBroadcastService {
    if (!SettingsBroadcastService.instance) {
      SettingsBroadcastService.instance = new SettingsBroadcastService();
    }
    return SettingsBroadcastService.instance;
  }

  private handleMessage(event: MessageEvent<SettingsRealtimeEvent>) {
    this.listeners.forEach((listener) => {
      try {
        listener(event.data);
      } catch (error) {
        console.warn('Settings broadcast listener error:', error);
      }
    });
  }

  broadcast(event: SettingsRealtimeEvent) {
    if (this.channel) {
      this.channel.postMessage(event);
    }
  }

  subscribe(listener: (event: SettingsRealtimeEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  destroy() {
    if (this.channel) {
      this.channel.close();
      this.channel = null;
    }
    this.listeners.clear();
  }
}

/**
 * Hook for optimized cross-tab settings synchronization
 * Uses granular updates instead of full reloads for bleeding-fast performance
 */
export function useSettingsBroadcast(tenantId: string, userId: string) {
  const broadcastService = SettingsBroadcastService.getInstance();

  useEffect(() => {
    const unsubscribe = broadcastService.subscribe(async (event) => {
      // Skip broadcast events since real-time subscriptions handle updates more efficiently
      // This prevents duplicate updates and improves performance
      // Real-time subscriptions provide instant updates, making cross-tab broadcasts redundant
      console.log('Cross-tab change detected:', event.table, event.eventType);
    });

    return unsubscribe;
  }, [broadcastService]);

  const broadcastChange = useCallback(
    (table: string, eventType: 'INSERT' | 'UPDATE' | 'DELETE') => {
      // Lightweight notification for other tabs (no data reload needed)
      broadcastService.broadcast({
        table,
        eventType,
      });
    },
    [broadcastService]
  );

  return { broadcastChange };
}
